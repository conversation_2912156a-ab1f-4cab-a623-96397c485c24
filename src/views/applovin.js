/**
 * 注意点：
 *  当您的 HTML 广告素材被加载到支持 MRAID 的移动应用 SDK 的 WebView 中时，
 *   这个 SDK 会在运行时自动将 mraid.js 的内容注入到您的 HTML 页面中。
 */

/*
常用 MRAID 命令：
        mraid.expand(): 请求将广告展开到全屏或一个更大的尺寸。
        mraid.close(): 关闭展开或调整过大小的广告，恢复到默认状态。
        mraid.open(url): 在一个新的浏览器窗口中打开一个链接。这对于点击跳转到 App Store 或外部网站至关重要。
        mraid.createCalendarEvent（parameters）： 创建日历事件。

        mraid.storePicture(url): 将图片保存到用户的相册。
        mraid.playVideo（url）： 播放视频。

事件监听：MRAID 提供了一套事件监听机制，让您的广告能够对环境变化做出反应。
        ready: MRAID 环境准备就绪。
        stateChange: 广告状态发生变化时触发。
        viewableChange: 广告的可见性发生变化时触发。
        error: 发生错误时触发。

*/


// 检查 MRAID 是否可用
if (typeof mraid !== 'undefined') {
    // MRAID 可用，可以继续执行 MRAID 相关逻辑
    if (mraid.getState() === 'loading') {
        mraid.addEventListener('ready', onMraidReady);
    } else {
        onMraidReady();
    }
} else {
    // MRAID 不可用，执行后备逻辑
    // 例如，对于点击跳转，可以直接使用 window.open()
    // 但要注意，在很多 App 的 WebView 中，window.open() 可能会被禁用
    console.log("MRAID is not available.");
}

const onMraidReady = () => {
    // 在这里安全地使用 mraid 对象
    console.log("MRAID is ready!");
    // 例如: 为一个按钮绑定 mraid.open() 事件
    let els = document.querySelectorAll('applovinElBtn')
    els.forEach((el,) => {
        el.addEventListener('click', (event) => {
            console.log("MRAID is click!");
            event.preventDefault()
            mraid.open()
        })
    })
}