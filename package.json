{"name": "h5", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode dev", "local": "vue-cli-service serve --mode local", "dev": "vue-cli-service serve --mode dev", "dev:https": "cross-env USE_HTTPS=true vue-cli-service serve --mode dev", "dev-overseas": "vue-cli-service serve --mode dev-overseas", "检测二级目录——海外测试、预发布、正式环境——是否成功运行": "", "dev:auto_jenkins": "cross-env PROJECT_SERVER_PATH=booktok_web vue-cli-service serve --mode dev", "---": "", "prod": "vue-cli-service serve --mode prod", "build:local": "vue-cli-service build --mode local", "build:dev": "vue-cli-service build --mode dev", "build:dev-overseas": "vue-cli-service build --mode dev-overseas", "build:prod": "vue-cli-service build --mode prod", "build": "vue-cli-service build --mode prod", "lint": "vue-cli-service lint", "build-overseas-magfic": "cross-env PROJECT_NAME=magfic PROJECT_ENV=overseas PROJECT_SCRIPT=buildScript  vue-cli-service build --mode dev", "zip-overseas-magfic": "cross-env PROJECT_NAME=magfic PROJECT_ENV=overseas node zipUtils/autoCreateZip.js", "build-zip-overseas-magfic": "yarn build-overseas-magfic && yarn zip-overseas-magfic", "build-overseas-goodfic": "cross-env PROJECT_NAME=goodfic PROJECT_ENV=overseas PROJECT_SCRIPT=buildScript  vue-cli-service build --mode dev", "zip-overseas-goodfic": "cross-env PROJECT_NAME=goodfic PROJECT_ENV=overseas node zipUtils/autoCreateZip.js", "build-zip-overseas-goodfic": "yarn build-overseas-goodfic && yarn zip-overseas-goodfic", "build-overseas-yestory": "cross-env PROJECT_NAME=yestory PROJECT_ENV=overseas PROJECT_SCRIPT=buildScript  vue-cli-service build --mode dev", "zip-overseas-yestory": "cross-env PROJECT_NAME=yestory PROJECT_ENV=overseas node zipUtils/autoCreateZip.js", "build-zip-overseas-yestory": "yarn build-overseas-yestory && yarn zip-overseas-yestory", "build-overseas-booktok": "cross-env PROJECT_NAME=booktok PROJECT_ENV=overseas PROJECT_SCRIPT=buildScript  vue-cli-service build --mode dev", "zip-overseas-booktok": "cross-env PROJECT_NAME=booktok PROJECT_ENV=overseas node zipUtils/autoCreateZip.js", "build-zip-overseas-booktok": "yarn build-overseas-booktok && yarn zip-overseas-booktok", "build-overseas-novelaria": "cross-env PROJECT_NAME=novelaria PROJECT_ENV=overseas PROJECT_SCRIPT=buildScript  vue-cli-service build --mode dev", "zip-overseas-novelaria": "cross-env PROJECT_NAME=novelaria PROJECT_ENV=overseas node zipUtils/autoCreateZip.js", "build-zip-overseas-novelaria": "yarn build-overseas-novelaria && yarn zip-overseas-novelaria", "build-overseas-netfic": "cross-env PROJECT_NAME=netfic PROJECT_ENV=overseas PROJECT_SCRIPT=buildScript  vue-cli-service build --mode dev", "zip-overseas-netfic": "cross-env PROJECT_NAME=netfic PROJECT_ENV=overseas node zipUtils/autoCreateZip.js", "build-zip-overseas-netfic": "yarn build-overseas-netfic && yarn zip-overseas-netfic", "build-production": "cross-env PROJECT_NAME=magfic PROJECT_ENV=production PROJECT_SCRIPT=buildScript  vue-cli-service build --mode prod", "zip-production": "cross-env PROJECT_NAME=magfic PROJECT_ENV=production node zipUtils/autoCreateZip.js", "build-zip-production": "yarn build-production && yarn zip-production && open ./", "build-production-yestory": "cross-env PROJECT_NAME=yestory PROJECT_ENV=production PROJECT_SCRIPT=buildScript  vue-cli-service build --mode prod", "zip-production-yestory": "cross-env PROJECT_NAME=yestory PROJECT_ENV=production node zipUtils/autoCreateZip.js", "build-zip-production-yestory": "yarn build-production-yestory && yarn zip-production-yestory", "build-production-magfic": "cross-env PROJECT_NAME=magfic PROJECT_ENV=production PROJECT_SCRIPT=buildScript  vue-cli-service build --mode prod", "zip-production-magfic": "cross-env PROJECT_NAME=magfic PROJECT_ENV=production node zipUtils/autoCreateZip.js", "build-zip-production-magfic": "yarn build-production-magfic && yarn zip-production-magfic", "build-production-goodfic": "cross-env PROJECT_NAME=goodfic PROJECT_ENV=production PROJECT_SCRIPT=buildScript  vue-cli-service build --mode prod", "zip-production-goodfic": "cross-env PROJECT_NAME=goodfic PROJECT_ENV=production node zipUtils/autoCreateZip.js", "build-zip-production-goodfic": "yarn build-production-goodfic && yarn zip-production-goodfic", "build-production-booktok": "cross-env PROJECT_NAME=booktok PROJECT_ENV=production PROJECT_SCRIPT=buildScript  vue-cli-service build --mode prod", "zip-production-booktok": "cross-env PROJECT_NAME=booktok PROJECT_ENV=production node zipUtils/autoCreateZip.js", "build-zip-production-booktok": "yarn build-production-booktok && yarn zip-production-booktok", "build-production-novelaria": "cross-env PROJECT_NAME=novelaria PROJECT_ENV=production PROJECT_SCRIPT=buildScript  vue-cli-service build --mode prod", "zip-production-novelaria": "cross-env PROJECT_NAME=novelaria PROJECT_ENV=production node zipUtils/autoCreateZip.js", "build-zip-production-novelaria": "yarn build-production-novelaria && yarn zip-production-novelaria", "build-production-netfic": "cross-env PROJECT_NAME=netfic PROJECT_ENV=production PROJECT_SCRIPT=buildScript  vue-cli-service build --mode prod", "zip-production-netfic": "cross-env PROJECT_NAME=netfic PROJECT_ENV=production node zipUtils/autoCreateZip.js", "build-zip-production-netfic": "yarn build-production-netfic && yarn zip-production-netfic", "~~~自定义移除dist/img目录~~~": "", "build-custom-remove-distImg": "cross-env PROJECT_NAME=${PROJECT_NAME} node scripts/rm-dist-custom.js", "~~~打包多个正式~~~": "", "build-zip-rm-all-zip": "rimraf dist *.zip", "set-build-time": "export BUILD_TIME=$(date '+%Y_%m_%d__%H_%M_%S')", "build-zip-create-production-magfic": "BUILD_TIME=$(date '+%Y_%m_%d__%H_%M_%S') && yarn build-production-magfic && cross-env PROJECT_NAME=magfic yarn build-custom-remove-distImg && zip -r magfic_production_$BUILD_TIME.zip dist && echo $BUILD_TIME > .build_time", "build-zip-create-production-yestory": "BUILD_TIME=$(cat .build_time) && yarn build-production-yestory && cross-env PROJECT_NAME=yestory yarn build-custom-remove-distImg && zip -r yestory_production_$BUILD_TIME.zip dist", "build-zip-create-production-goodfic": "BUILD_TIME=$(cat .build_time) && yarn build-production-goodfic && cross-env PROJECT_NAME=goodfic yarn build-custom-remove-distImg && zip -r goodfic_production_$BUILD_TIME.zip dist", "build-zip-create-production-booktok": "BUILD_TIME=$(cat .build_time) && yarn build-production-booktok && cross-env PROJECT_NAME=booktok yarn build-custom-remove-distImg && zip -r booktok_production_$BUILD_TIME.zip dist", "build-zip-create-production-novelaria": "BUILD_TIME=$(cat .build_time) && yarn build-production-novelaria && cross-env PROJECT_NAME=novelaria yarn build-custom-remove-distImg && zip -r novelaria_production_$BUILD_TIME.zip dist", "build-zip-create-production-netfic": "BUILD_TIME=$(cat .build_time) && yarn build-production-netfic && cross-env PROJECT_NAME=netfic yarn build-custom-remove-distImg && zip -r netfic_production_$BUILD_TIME.zip dist", "build-zip-create-production-zip": "BUILD_TIME=$(cat .build_time) && rimraf production_5size_* && mkdir production_5size_$BUILD_TIME && mv *.zip production_5size_$BUILD_TIME && rm .build_time && open ./", "build-zip-run-production": "yarn build-zip-rm-all-zip && yarn build-zip-create-production-magfic && yarn build-zip-create-production-yestory && yarn build-zip-create-production-goodfic && yarn build-zip-create-production-booktok && yarn build-zip-create-production-novelaria && yarn build-zip-create-production-netfic && yarn build-zip-create-production-zip", "~~~打包多个测试~~~": "", "build-zip-create-overseas-magfic": "BUILD_TIME=$(date '+%Y_%m_%d__%H_%M_%S') && yarn build-overseas-magfic && cross-env PROJECT_NAME=magfic yarn build-custom-remove-distImg && zip -r magfic_overseas_$BUILD_TIME.zip dist && echo $BUILD_TIME > .build_time", "build-zip-create-overseas-yestory": "BUILD_TIME=$(cat .build_time) && yarn build-overseas-yestory && cross-env PROJECT_NAME=yestory yarn build-custom-remove-distImg && zip -r yestory_overseas_$BUILD_TIME.zip dist", "build-zip-create-overseas-goodfic": "BUILD_TIME=$(cat .build_time) && yarn build-overseas-goodfic && cross-env PROJECT_NAME=goodfic yarn build-custom-remove-distImg && zip -r goodfic_overseas_$BUILD_TIME.zip dist", "build-zip-create-overseas-booktok": "BUILD_TIME=$(cat .build_time) && yarn build-overseas-booktok && cross-env PROJECT_NAME=booktok yarn build-custom-remove-distImg && zip -r booktok_overseas_$BUILD_TIME.zip dist", "build-zip-create-overseas-novelaria": "BUILD_TIME=$(cat .build_time) && yarn build-overseas-novelaria && cross-env PROJECT_NAME=novelaria yarn build-custom-remove-distImg && zip -r novelaria_overseas_$BUILD_TIME.zip dist", "build-zip-create-overseas-netfic": "BUILD_TIME=$(cat .build_time) && yarn build-overseas-netfic && cross-env PROJECT_NAME=netfic yarn build-custom-remove-distImg && zip -r netfic_overseas_$BUILD_TIME.zip dist", "build-zip-create-overseas-zip": "BUILD_TIME=$(cat .build_time) && rimraf overseas_5size_* && mkdir overseas_5size_$BUILD_TIME && mv *.zip overseas_5size_$BUILD_TIME && rm .build_time && open ./", "build-zip-run-overseas": "yarn build-zip-rm-all-zip && yarn build-zip-create-overseas-magfic && yarn build-zip-create-overseas-yestory && yarn build-zip-create-overseas-goodfic && yarn build-zip-create-overseas-booktok && yarn build-zip-create-overseas-novelaria && yarn build-zip-create-overseas-netfic && yarn build-zip-create-overseas-zip", "upload": "node ~/2024/gua-ssh-deployment-util/index.js", "========部署到我自己的个人服务器========": "", "rm-result": "rimraf dist h5popularize_overseas_public_*.zip", "s-1": "cross-env PROJECT_NAME=goodfic PROJECT_ENV=overseas PROJECT_SCRIPT=buildScript PROJECT_SERVER_PATH=h5popularize_wangwen_public vue-cli-service build --mode dev", "s-2": "zip -r h5popularize_overseas_public_$(date '+%Y_%m_%d__%H_%M_%S').zip dist", "s-3": "node ~/2024/gua-ssh-deployment-util/index.js -n myTest_国外_网文h5落地页", "upload-overseas-h5popularize-wangwen": "yarn rm-result && yarn s-1 && yarn s-2 && yarn s-3", "look": "serve -s dist -l 7082", "===jenkis部署===": "", "build-overseas-magfic-jenkis": "cross-env PROJECT_NAME=magfic PROJECT_ENV=overseas PROJECT_SERVER_PATH=magfic_web PROJECT_SCRIPT=buildScript vue-cli-service build --mode dev", "build-production-magfic-jenkis": "cross-env PROJECT_NAME=magfic PROJECT_ENV=production PROJECT_SERVER_PATH=magfic_web PROJECT_SCRIPT=buildScript vue-cli-service build --mode dev", "build-overseas-goodfic-jenkis": "cross-env PROJECT_NAME=goodfic PROJECT_ENV=overseas PROJECT_SERVER_PATH=goodfic_web PROJECT_SCRIPT=buildScript vue-cli-service build --mode dev", "build-production-goodfic-jenkis": "cross-env PROJECT_NAME=goodfic PROJECT_ENV=production PROJECT_SERVER_PATH=goodfic_web PROJECT_SCRIPT=buildScript vue-cli-service build --mode dev", "build-overseas-yestory-jenkis": "cross-env PROJECT_NAME=yestory PROJECT_ENV=overseas PROJECT_SERVER_PATH=yestory_web PROJECT_SCRIPT=buildScript vue-cli-service build --mode dev", "build-production-yestory-jenkis": "cross-env PROJECT_NAME=yestory PROJECT_ENV=production PROJECT_SERVER_PATH=yestory_web PROJECT_SCRIPT=buildScript vue-cli-service build --mode dev", "build-overseas-novelaria-jenkis": "cross-env PROJECT_NAME=novelaria PROJECT_ENV=overseas PROJECT_SERVER_PATH=novelaria_web PROJECT_SCRIPT=buildScript vue-cli-service build --mode dev", "build-production-novelaria-jenkis": "cross-env PROJECT_NAME=novelaria PROJECT_ENV=production PROJECT_SERVER_PATH=novelaria_web PROJECT_SCRIPT=buildScript vue-cli-service build --mode dev", "build-overseas-booktok-jenkis": "cross-env PROJECT_NAME=booktok PROJECT_ENV=overseas PROJECT_SERVER_PATH=booktok_web PROJECT_SCRIPT=buildScript vue-cli-service build --mode dev", "build-production-booktok-jenkis": "cross-env PROJECT_NAME=booktok PROJECT_ENV=production PROJECT_SERVER_PATH=booktok_web PROJECT_SCRIPT=buildScript vue-cli-service build --mode dev", "build-overseas-netfic-jenkis": "cross-env PROJECT_NAME=netfic PROJECT_ENV=overseas PROJECT_SERVER_PATH=netfic_web PROJECT_SCRIPT=buildScript vue-cli-service build --mode dev", "build-production-netfic-jenkis": "cross-env PROJECT_NAME=netfic PROJECT_ENV=production PROJECT_SERVER_PATH=netfic_web PROJECT_SCRIPT=buildScript vue-cli-service build --mode dev"}, "dependencies": {"archiver": "^6.0.0", "axios": "^0.24.0", "clipboard": "^2.0.11", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "dayjs": "^1.11.10", "disable-devtool": "^0.3.3", "dsbridge": "^3.1.4", "file-loader": "^6.2.0", "html2canvas": "^1.4.1", "lodash": "^4.17.21", "loglevel": "^1.9.2", "lottie-web": "^5.12.2", "moment-timezone": "^0.5.38", "postcss": "^8.4.5", "postcss-px-to-viewport": "^1.1.1", "qs": "^6.10.3", "raw-loader": "^4.0.2", "rimraf": "^6.0.1", "thinkingdata-browser": "^1.6.1", "vant": "^2.12.38", "vconsole": "^3.15.0", "velocity-animate": "^1.5.2", "vue": "^2.6.11", "vue-clipboard2": "^0.3.3", "vue-i18n": "^8.27.0", "vue-router": "^3.2.0", "vuex": "^3.4.0", "vuex-persistedstate": "^4.1.0", "webpack-cli": "^4.10.0", "webpack-merge": "^5.8.0", "webpack-spritesmith": "^1.1.0", "sass": "^1.56.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "^5.0.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@babel/eslint-parser": "^7.19.0", "cross-env": "^7.0.3", "eslint": "^7.5.0", "eslint-plugin-vue": "^7.0.0", "fflate": "^0.8.1", "mockjs": "^1.1.0", "sass-loader": "^8.0.2", "svga-loader": "^0.2.1", "vue-template-compiler": "^2.6.11", "webpack": "^4.44.2", "webpack-bundle-analyzer": "^4.5.0", "eslint-webpack-plugin": "^2.5.4"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}